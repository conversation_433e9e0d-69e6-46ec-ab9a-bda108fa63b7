// Test the slider logic for draggable questions

// Sample data from Strapi
const question1 = {
  id: 16,
  name: 'Approximately how many servers do you intend to migrate?',
  type: 'draggable',
  number: 2,
  answers: [
    { id: 108, name: 'Less than 10', value: 10000 },
    { id: 109, name: '10 – 50', value: 50000 },
    { id: 110, name: '50 – 100', value: 250000 },
    { id: 111, name: 'More than 100', value: 500000 }
  ]
};

const question2 = {
  id: 27,
  name: 'What is the average CPU and memory usage of your workloads?',
  type: 'draggable',
  number: 9,
  answers: [
    { id: 151, name: 'Low (minimal utilization)', value: 0 },
    { id: 152, name: 'Medium', value: 0 },
    { id: 153, name: 'High (intensive usage)', value: 0 }
  ]
};

function getSliderValueFromAnswer(question, answerName) {
  if (!question || !question.answers) return 0;

  if (question.type === 'draggable') {
    const answerIndex = question.answers.findIndex(ans => ans.name === answerName);
    if (answerIndex === -1) return 0;
    
    const stepSize = 100 / (question.answers.length - 1);
    const sliderValue = answerIndex * stepSize;
    console.log(`${question.name}: ${answerName} -> slider value ${sliderValue}`);
    return sliderValue;
  }
  return 0;
}

function getAnswerName(question, value) {
  if (!question || !question.answers) return '';

  if (question.type === 'draggable') {
    const stepSize = 100 / (question.answers.length - 1);
    let ind = Math.round(value / stepSize);
    
    ind = Math.max(0, Math.min(ind, question.answers.length - 1));
    
    const answerName = question.answers[ind].name;
    console.log(`${question.name}: slider value ${value} -> ${answerName} (index ${ind}, stepSize ${stepSize})`);
    return answerName;
  }
  return '';
}

// Test question 1 (4 answers)
console.log('\n=== Testing Question 1 (4 answers) ===');
console.log('Expected step size:', 100 / (question1.answers.length - 1)); // 33.33

// Test forward mapping (answer name to slider value)
question1.answers.forEach((answer, index) => {
  const sliderValue = getSliderValueFromAnswer(question1, answer.name);
  console.log(`Answer ${index}: "${answer.name}" -> ${sliderValue}%`);
});

// Test reverse mapping (slider value to answer name)
console.log('\nTesting slider positions:');
[0, 33, 67, 100].forEach(value => {
  const answerName = getAnswerName(question1, value);
  console.log(`Slider ${value}% -> "${answerName}"`);
});

// Test question 2 (3 answers)
console.log('\n=== Testing Question 2 (3 answers) ===');
console.log('Expected step size:', 100 / (question2.answers.length - 1)); // 50

// Test forward mapping
question2.answers.forEach((answer, index) => {
  const sliderValue = getSliderValueFromAnswer(question2, answer.name);
  console.log(`Answer ${index}: "${answer.name}" -> ${sliderValue}%`);
});

// Test reverse mapping
console.log('\nTesting slider positions:');
[0, 50, 100].forEach(value => {
  const answerName = getAnswerName(question2, value);
  console.log(`Slider ${value}% -> "${answerName}"`);
});

// Test edge cases
console.log('\n=== Testing Edge Cases ===');
console.log('Slider 25% (between 0 and 50):', getAnswerName(question2, 25));
console.log('Slider 75% (between 50 and 100):', getAnswerName(question2, 75));
console.log('Slider 10% (close to 0):', getAnswerName(question2, 10));
console.log('Slider 90% (close to 100):', getAnswerName(question2, 90));
